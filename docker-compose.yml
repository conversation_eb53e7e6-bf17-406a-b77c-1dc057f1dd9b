services:
  ingress:
    build: ./ingress
    image: "${INGRESS_IMAGE}"
    depends_on:
      - backend
      - frontend
    ports:
      - "${INGRESS_PORT}:80"

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: products
      POSTGRES_PASSWORD: "${DB_APP_PASSWORD}"
      TZ: "America/Argentina/Buenos_Aires"
    volumes:
      - "${VOLUME_DIR}/data:/var/lib/postgresql/data"
    ports:
      - "${DB_PORT}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d products"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build: ./backend
    image: "${BACKEND_IMAGE}"
    depends_on:
      db:
        condition: service_healthy
    environment:
      SPRING_DATASOURCE_USERNAME: postgres
      SPRING_DATASOURCE_PASSWORD: "${DB_APP_PASSWORD}"
      SPRING_DATASOURCE_URL: "**********************************"
      SPRING_DATASOURCE_DRIVER_CLASS_NAME: "org.postgresql.Driver"

  frontend:
    build: ./frontend
    image: "${FRONTEND_IMAGE}"
    depends_on:
      - backend
    environment:
      BACKEND_EXTERNAL_URL: "${EXTERNAL_URL}/api"

  adminer:
    image: adminer:5.4.0
    depends_on:
      - db
    ports:
      - "${ADMINER_EXTERNAL_PORT}:8080"
