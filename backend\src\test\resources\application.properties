spring.application.name=product-example

spring.jpa.hibernate.ddl-auto=update
spring.jpa.open-in-view=false

spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

jwt.access.secret=0000000000000000000000000000000000000000000000000000000000000000
jwt.access.expiration=1800000
jwt.refresh.bytes=20
jwt.refresh.expiration=2592000000

debug=true
