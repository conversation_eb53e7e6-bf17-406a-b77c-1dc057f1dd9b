package ar.uba.fi.ingsoft1.product_example.products;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/products")
@Validated
@RequiredArgsConstructor
class ProductRestController {
    private final ProductService productService;

    @GetMapping
    public List<ProductDTO> getProducts(ProductSearchDTO filter) {
        return productService.getProducts(filter);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ProductDTO> getProductById(@PathVariable long id) {
        return productService.getProductById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND).build());
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ProductDTO createProduct(
            @NonNull @RequestBody ProductCreateDTO data
    ) {
        return productService.createProduct(data);
    }

    @PatchMapping("/{id}")
    public Optional<ProductDTO> updateProduct(
            @PathVariable Long id,
            @NonNull @RequestBody ProductUpdateDTO data
    ) {
        return productService.updateProduct(id, data);
    }
}
