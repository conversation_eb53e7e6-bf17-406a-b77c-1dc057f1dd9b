FROM maven:3.9.9-eclipse-temurin-21 AS build
WORKDIR /home/<USER>
COPY pom.xml pom.xml
RUN mvn dependency:go-offline
COPY . .
RUN mvn package -DskipTests

FROM eclipse-temurin:21-jre AS deploy
WORKDIR /home/<USER>
COPY --from=build /home/<USER>/target/product-template-0.0.1-SNAPSHOT.jar /home/<USER>/product-template-0.0.1-SNAPSHOT.jar
EXPOSE 8080
ENTRYPOINT ["java","-jar","/home/<USER>/product-template-0.0.1-SNAPSHOT.jar"]