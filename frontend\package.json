{"name": "todo-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -p tsconfig.app.json && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@tanstack/react-form": "^1.1.0", "@tanstack/react-query": "^5.69.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^5.0.4", "react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "~5.9.2", "vite": "^7.1.7", "vite-tsconfig-paths": "^5.1.4", "wouter": "^3.6.0", "zod": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.21.0", "@testing-library/jest-dom": "^6.6.2", "@testing-library/react": "^16.0.1", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.4.0", "happy-dom": "^19.0.1", "typescript-eslint": "^8.24.1", "vitest": "^3.2.4"}}