{
  "compilerOptions": {
    "target": "ES2022",
    "skipLibCheck": true,
    "useDefineForClassFields": true,

    /* Bundler mode */
    "module": "esnext",
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,

    /* Imports */
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
